#!/usr/bin/env python3
"""
Strategy Engine for Trading Bot
Evaluates all four strategies and provides trading signals
"""

import pandas as pd
import numpy as np

from datetime import datetime
from config import STRATEGY_CONFIG, TRADING_CONFIG
from utils import print_colored, format_price

class StrategyEngine:
    def __init__(self):
        """Initialize the strategy engine"""
        self.strategies = STRATEGY_CONFIG
        self.load_models()
        
    def load_models(self):
        """Initialize strategy engine (no models needed for rule-based strategies)"""
        print_colored("🔧 Initializing rule-based strategy engine...", "INFO")
        print_colored("✅ Strategy engine ready", "SUCCESS")

    def evaluate_strategy_1(self, df):
        """Strategy 1: MOMENTUM BREAKOUT - IMPROVED FOR HIGH ACCURACY"""
        if len(df) < 30:  # More data for better analysis
            return 0, 0.0

        try:
            current = df.iloc[-1]
            prev = df.iloc[-2]
            prev2 = df.iloc[-3]

            # Current candle data
            close = current['close']
            open_ = current['open']
            high = current['high']
            low = current['low']
            volume = current['volume']

            # Enhanced key levels calculation
            lookback = 20  # Longer lookback for better levels
            recent_highs = df['high'].tail(lookback)
            recent_lows = df['low'].tail(lookback)
            resistance = recent_highs.max()
            support = recent_lows.min()

            # Moderate volume confirmation - balanced for signals
            vol_avg = df['volume'].tail(10).mean()
            volume_surge = volume > vol_avg * 1.3  # Moderate volume requirement

            # Technical indicators
            rsi = current['rsi'] if 'rsi' in current and not pd.isna(current['rsi']) else 50
            ema_20 = current['ema_20'] if 'ema_20' in current and not pd.isna(current['ema_20']) else close

            # Strong candle analysis - require very strong candles
            body = abs(close - open_)
            total_range = high - low
            body_ratio = body / total_range if total_range > 0 else 0

            # Require strong directional candles - balanced
            strong_bullish = (close > open_ and body_ratio > 0.5)  # Strong bull candle
            strong_bearish = (close < open_ and body_ratio > 0.5)  # Strong bear candle

            # Multi-candle momentum confirmation - need 2 candles in same direction
            prev_bullish = prev['close'] > prev['open']
            prev2_bullish = prev2['close'] > prev2['open']
            momentum_confirmed_up = strong_bullish and prev_bullish
            momentum_confirmed_down = strong_bearish and (not prev_bullish)

            # Price action confirmation - moderate breakout/breakdown
            clear_breakout = close > resistance * 1.0002  # Moderate breakout above resistance
            clear_breakdown = close < support * 0.9998   # Moderate breakdown below support

            # Trend alignment - moderate
            trend_up = close > ema_20 * 1.001  # Moderate uptrend
            trend_down = close < ema_20 * 0.999  # Moderate downtrend

            # PREMIUM QUALITY BUY Signal - High accuracy focus
            if (close > open_ and  # Bullish candle
                close > ema_20 * 1.0005 and  # Well above EMA (0.05%)
                current['volume'] > vol_avg * 1.25 and  # Strong volume increase
                57 < rsi < 73 and  # Tight RSI range (sweet spot)
                body > (high - low) * 0.35 and  # Strong candle body
                close > prev['close'] and  # Momentum confirmation
                high > prev['high']):  # Higher high confirmation
                return 1, 0.82  # Very high confidence

            # PREMIUM QUALITY SELL Signal - High accuracy focus
            elif (close < open_ and  # Bearish candle
                  close < ema_20 * 0.9995 and  # Well below EMA (0.05%)
                  current['volume'] > vol_avg * 1.25 and  # Strong volume increase
                  27 < rsi < 43 and  # Tight RSI range (sweet spot)
                  body > (high - low) * 0.35 and  # Strong candle body
                  close < prev['close'] and  # Momentum confirmation
                  low < prev['low']):  # Lower low confirmation
                return -1, 0.82  # Very high confidence

            return 0, 0.0

        except Exception as e:
            print_colored(f"❌ Error in Strategy 1: {str(e)}", "ERROR")
            return 0, 0.0

    def evaluate_strategy_2(self, df):
        """Strategy 2: PULLBACK ENTRY - IMPROVED FOR HIGH ACCURACY"""
        if len(df) < 25:
            return 0, 0.0

        try:
            current = df.iloc[-1]
            prev = df.iloc[-2]
            prev2 = df.iloc[-3]

            # Current candle data
            close = current['close']
            open_ = current['open']
            high = current['high']
            low = current['low']
            volume = current['volume']

            # Technical indicators
            ema_20 = current['ema_20'] if 'ema_20' in current and not pd.isna(current['ema_20']) else close
            rsi = current['rsi'] if 'rsi' in current and not pd.isna(current['rsi']) else 50

            # Enhanced candle structure analysis
            body = abs(close - open_)
            total_range = high - low
            upper_wick = high - max(open_, close)
            lower_wick = min(open_, close) - low
            body_ratio = body / total_range if total_range > 0 else 0

            # Moderate volume confirmation
            vol_avg = df['volume'].tail(15).mean()
            volume_surge = volume > vol_avg * 1.3  # Moderate volume requirement

            # Good pullback detection - close to EMA20
            pullback_tolerance = 0.005  # 0.5% - balanced
            good_pullback = abs(close - ema_20) / ema_20 < pullback_tolerance if ema_20 > 0 else False

            # Good trend confirmation - moderate consistency
            recent_closes = df['close'].tail(6)
            good_uptrend = sum(recent_closes.iloc[i] > recent_closes.iloc[i-1] for i in range(1, len(recent_closes))) >= 4
            good_downtrend = sum(recent_closes.iloc[i] < recent_closes.iloc[i-1] for i in range(1, len(recent_closes))) >= 4

            # EMA trend should be clear
            ema_values = df['ema_20'].tail(6)
            ema_rising = sum(ema_values.iloc[i] > ema_values.iloc[i-1] for i in range(1, len(ema_values))) >= 4
            ema_falling = sum(ema_values.iloc[i] < ema_values.iloc[i-1] for i in range(1, len(ema_values))) >= 4

            # Price must be in right position relative to EMA
            price_above_ema = close > ema_20 * 1.001  # Slightly above EMA
            price_below_ema = close < ema_20 * 0.999  # Slightly below EMA

            # Strong candle requirements
            strong_bullish = (close > open_ and body_ratio > 0.6)  # Strong bull candle
            strong_bearish = (close < open_ and body_ratio > 0.6)  # Strong bear candle

            # Multi-candle confirmation
            prev_supports_trend_up = prev['close'] > prev['open']  # Previous candle bullish
            prev_supports_trend_down = prev['close'] < prev['open']  # Previous candle bearish

            # Momentum confirmation
            momentum_up = close > prev['close'] * 1.0008  # Strong upward momentum
            momentum_down = close < prev['close'] * 0.9992  # Strong downward momentum

            # OPTIMIZED BUY: Higher accuracy pullback
            if (close > open_ and               # Bullish candle
                close > ema_20 * 1.0004 and    # Well above EMA
                current['volume'] > vol_avg * 1.15 and     # Strong volume
                53 < rsi < 67 and              # Tight RSI range
                body > (high - low) * 0.28 and # Strong candle body
                close > prev['close'] and      # Momentum confirmation
                high > prev['high']):          # Higher high confirmation
                return 1, 0.78

            # OPTIMIZED SELL: Higher accuracy pullback
            elif (close < open_ and             # Bearish candle
                  close < ema_20 * 0.9996 and  # Well below EMA
                  current['volume'] > vol_avg * 1.15 and   # Strong volume
                  33 < rsi < 47 and            # Tight RSI range
                  body > (high - low) * 0.28 and # Strong candle body
                  close < prev['close'] and    # Momentum confirmation
                  low < prev['low']):          # Lower low confirmation
                return -1, 0.78

            return 0, 0.0

        except Exception as e:
            print_colored(f"❌ Error in Strategy 2: {str(e)}", "ERROR")
            return 0, 0.0

    def calculate_atr(self, df, period=14):
        """Calculate Average True Range"""
        high_low = df['high'] - df['low']
        high_close = np.abs(df['high'] - df['close'].shift())
        low_close = np.abs(df['low'] - df['close'].shift())
        tr = pd.concat([high_low, high_close, low_close], axis=1).max(axis=1)
        return tr.rolling(period).mean()

    def calculate_rsi_custom(self, df, period=14):
        """Calculate RSI if not available"""
        delta = df['close'].diff()
        gain = delta.where(delta > 0, 0)
        loss = -delta.where(delta < 0, 0)

        avg_gain = gain.rolling(period).mean()
        avg_loss = loss.rolling(period).mean()

        rs = avg_gain / avg_loss
        return 100 - (100 / (1 + rs))

    def evaluate_strategy_3(self, df):
        """Strategy 3: BINARY REVERSAL - IMPROVED FOR HIGH ACCURACY"""
        if len(df) < 30:
            return 0, 0.0

        try:
            # Calculate indicators if needed
            if 'rsi' not in df.columns:
                df['rsi'] = self.calculate_rsi_custom(df)

            current = df.iloc[-1]
            prev = df.iloc[-2]
            prev2 = df.iloc[-3]

            # Current candle metrics
            close, open_ = current['close'], current['open']
            high, low = current['high'], current['low']
            body = abs(close - open_)
            upper_wick = high - max(open_, close)
            lower_wick = min(open_, close) - low
            total_range = high - low

            # Moderate volume confirmation
            vol_avg = df['volume'].tail(15).mean()
            volume_surge = current['volume'] > vol_avg * 1.4  # Moderate volume requirement

            # Enhanced wick analysis - balanced
            wick_ratio_upper = upper_wick / (body + 0.00001)
            wick_ratio_lower = lower_wick / (body + 0.00001)

            # Strong rejection patterns
            strong_upper_rejection = (wick_ratio_upper > 2.0) and (upper_wick > total_range * 0.4)
            strong_lower_rejection = (wick_ratio_lower > 2.0) and (lower_wick > total_range * 0.4)

            # Good key levels - moderate lookback
            lookback = 15
            recent_high = df['high'].tail(lookback).max()
            recent_low = df['low'].tail(lookback).min()
            near_key_high = abs(high - recent_high) / recent_high < 0.004  # Close to high
            near_key_low = abs(low - recent_low) / recent_low < 0.004     # Close to low

            # Good RSI conditions
            rsi = current['rsi']
            overbought = rsi > 70
            oversold = rsi < 30

            # Multi-candle momentum confirmation
            strong_momentum_up = (df['close'].iloc[-1] > df['close'].iloc[-2] > df['close'].iloc[-3])
            strong_momentum_down = (df['close'].iloc[-1] < df['close'].iloc[-2] < df['close'].iloc[-3])

            # Reversal confirmation - price must close opposite to momentum
            clear_reversal_down = strong_momentum_up and (close < open_) and (body > total_range * 0.3)
            clear_reversal_up = strong_momentum_down and (close > open_) and (body > total_range * 0.3)

            # Support/Resistance test confirmation
            resistance_test = high >= recent_high * 0.9995  # Touched resistance
            support_test = low <= recent_low * 1.0005       # Touched support

            # PREMIUM SELL: High accuracy rejection
            if (upper_wick > body * 1.2 and    # Good upper wick (less strict)
                rsi > 63 and                   # High RSI (less strict)
                current['volume'] > vol_avg * 1.2 and  # Good volume (less strict)
                (near_key_high or strong_upper_rejection)):  # Either condition
                return -1, 0.80

            # PREMIUM BUY: High accuracy rejection
            elif (lower_wick > body * 1.2 and  # Good lower wick (less strict)
                  rsi < 37 and                 # Low RSI (less strict)
                  current['volume'] > vol_avg * 1.2 and  # Good volume (less strict)
                  (near_key_low or strong_lower_rejection)):  # Either condition
                return 1, 0.80

            # GOOD SELL: Moderate rejection pattern
            elif (upper_wick > body and         # Upper wick bigger than body
                  rsi > 65 and                 # High RSI (less strict)
                  current['volume'] > vol_avg * 1.1 and  # Light volume (less strict)
                  close < open_):              # Bearish candle
                return -1, 0.75

            # GOOD BUY: Moderate rejection pattern
            elif (lower_wick > body and         # Lower wick bigger than body
                  rsi < 35 and                 # Low RSI (less strict)
                  current['volume'] > vol_avg * 1.1 and  # Light volume (less strict)
                  close > open_):              # Bullish candle
                return 1, 0.75

            return 0, 0.0

        except Exception as e:
            print_colored(f"❌ Error in Strategy 3: {str(e)}", "ERROR")
            return 0, 0.0

    def evaluate_strategy_4(self, df):
        """Strategy 4: TREND CONFIRMATION - IMPROVED FOR HIGH ACCURACY"""
        if len(df) < 25:  # Moderate data for trend analysis
            return 0, 0.0

        try:
            current = df.iloc[-1]
            prev = df.iloc[-2]

            # Current candle data
            close = current['close']
            open_ = current['open']
            high = current['high']
            low = current['low']
            volume = current['volume']

            # Technical indicators
            rsi = current['rsi'] if 'rsi' in current and not pd.isna(current['rsi']) else 50
            ema_20 = current['ema_20'] if 'ema_20' in current and not pd.isna(current['ema_20']) else close

            # Moderate volume confirmation
            vol_avg = df['volume'].tail(15).mean()
            volume_surge = volume > vol_avg * 1.1  # Moderate volume requirement

            # Good trend strength analysis - moderate lookback
            lookback = 8
            recent_closes = df['close'].tail(lookback)
            recent_highs = df['high'].tail(lookback)
            recent_lows = df['low'].tail(lookback)

            # Very strong uptrend confirmation - need consistent trend
            uptrend_strength = sum(recent_closes.iloc[i] > recent_closes.iloc[i-1] for i in range(1, len(recent_closes)))
            higher_lows = sum(recent_lows.iloc[i] > recent_lows.iloc[i-1] for i in range(1, len(recent_lows)))
            higher_highs = sum(recent_highs.iloc[i] > recent_highs.iloc[i-1] for i in range(1, len(recent_highs)))

            # Very strong downtrend confirmation
            downtrend_strength = sum(recent_closes.iloc[i] < recent_closes.iloc[i-1] for i in range(1, len(recent_closes)))
            lower_highs = sum(recent_highs.iloc[i] < recent_highs.iloc[i-1] for i in range(1, len(recent_highs)))
            lower_lows = sum(recent_lows.iloc[i] < recent_lows.iloc[i-1] for i in range(1, len(recent_lows)))

            # Strong candle requirements
            body = abs(close - open_)
            total_range = high - low
            body_ratio = body / total_range if total_range > 0 else 0

            # Strong candle confirmation
            strong_bullish = (close > open_ and body_ratio > 0.5)  # Strong bull candle
            strong_bearish = (close < open_ and body_ratio > 0.5)  # Strong bear candle

            # Good EMA trend confirmation
            above_ema = close > ema_20 * 1.002  # Above EMA
            below_ema = close < ema_20 * 0.998  # Below EMA

            # EMA slope should be clear
            ema_values = df['ema_20'].tail(6)
            ema_rising = sum(ema_values.iloc[i] > ema_values.iloc[i-1] for i in range(1, len(ema_values))) >= 4
            ema_falling = sum(ema_values.iloc[i] < ema_values.iloc[i-1] for i in range(1, len(ema_values))) >= 4

            # Good price momentum confirmation
            momentum_up = close > df['close'].iloc[-4] * 1.003  # 0.3% move in 4 candles
            momentum_down = close < df['close'].iloc[-4] * 0.997  # 0.3% move in 4 candles

            # Multi-timeframe confirmation
            short_trend_up = df['close'].tail(3).iloc[-1] > df['close'].tail(3).iloc[0]
            short_trend_down = df['close'].tail(3).iloc[-1] < df['close'].tail(3).iloc[0]

            # Consecutive candle confirmation
            prev_bullish = prev['close'] > prev['open']
            prev_bearish = prev['close'] < prev['open']

            # HIGH ACCURACY TREND BUY: Selective trend following
            if (uptrend_strength >= 4 and      # Strong trend requirement
                close > open_ and              # Bullish candle
                close > ema_20 * 1.0005 and   # Well above EMA
                current['volume'] > vol_avg * 1.2 and    # Strong volume increase
                53 < rsi < 72 and             # Tight RSI range
                body > (high - low) * 0.3 and # Strong candle body
                ema_rising and                 # EMA trending up
                momentum_up):                  # Strong momentum confirmation
                return 1, 0.78

            # HIGH ACCURACY TREND SELL: Selective trend following
            elif (downtrend_strength >= 4 and  # Strong trend requirement
                  close < open_ and            # Bearish candle
                  close < ema_20 * 0.9995 and # Well below EMA
                  current['volume'] > vol_avg * 1.2 and  # Strong volume increase
                  28 < rsi < 47 and           # Tight RSI range
                  body > (high - low) * 0.3 and # Strong candle body
                  ema_falling and              # EMA trending down
                  momentum_down):              # Strong momentum confirmation
                return -1, 0.78

            return 0, 0.0

        except Exception as e:
            print_colored(f"❌ Error in Strategy 4: {str(e)}", "ERROR")
            return 0, 0.0

    def evaluate_all_strategies(self, df):
        """Evaluate all strategies and return combined signal"""
        signals = {}
        
        # Evaluate each strategy
        s1_signal, s1_conf = self.evaluate_strategy_1(df)
        s2_signal, s2_conf = self.evaluate_strategy_2(df)
        s3_signal, s3_conf = self.evaluate_strategy_3(df)
        s4_signal, s4_conf = self.evaluate_strategy_4(df)
        
        signals = {
            'S1': {'signal': s1_signal, 'confidence': s1_conf},
            'S2': {'signal': s2_signal, 'confidence': s2_conf},
            'S3': {'signal': s3_signal, 'confidence': s3_conf},
            'S4': {'signal': s4_signal, 'confidence': s4_conf}
        }
        
        # Find the strategy with highest confidence signal
        best_strategy = None
        best_signal = 0
        best_confidence = 0.0
        
        for strategy, data in signals.items():
            if data['signal'] != 0 and data['confidence'] > best_confidence:
                best_strategy = strategy
                best_signal = data['signal']
                best_confidence = data['confidence']
        
        # Return result
        if best_strategy and best_confidence >= TRADING_CONFIG['MIN_CONFIDENCE']:
            signal_name = 'BUY' if best_signal == 1 else 'SELL'
            return {
                'signal': signal_name,
                'confidence': best_confidence,
                'strategy': best_strategy,
                'price': df.iloc[-1]['close'],
                'all_signals': signals
            }
        else:
            return {
                'signal': 'HOLD',
                'confidence': 0.0,
                'strategy': None,
                'price': df.iloc[-1]['close'],
                'all_signals': signals
            }

    def calculate_rsi(self, prices, period=14):
        """Calculate RSI indicator"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi




