# 🤖 Advanced Trading Bot System

A comprehensive trading bot system with live market data integration, multiple trading strategies, and advanced backtesting capabilities.

## 🚀 Features

- **Live Trading**: Rule-based live trading with real-time market data
- **4 Trading Strategies**: Breakout, Pullback Entry, Support/Resistance, and Trend Following strategies
- **Advanced Signal Generator**: Pattern recognition and historical analysis
- **Backtesting System**: Comprehensive strategy testing and analysis
- **Colorful CLI**: Beautiful command-line interface with colored output
- **10 Currency Pairs**: EUR_USD, GBP_USD, USD_JPY, AUD_USD, USD_CAD, AUD_CAD, EUR_JPY, GBP_JPY, USD_CHF, EUR_GBP

## 📁 File Structure

### Core Files
- `trading_bot_launcher.py` - Main launcher with menu system
- `live_trading_bot.py` - Live trading bot with real-time signals
- `backtest_system.py` - Backtesting system for strategy analysis
- `strategy_engine.py` - Core strategy engine with 4 trading strategies
- `advanced_signal_generator.py` - Advanced pattern recognition and signal generation
- `config.py` - Configuration settings and API credentials
- `utils.py` - Utility functions and helpers

### Analysis Files
- `pattern_analyzer.py` - Pattern recognition and analysis
- `advanced_filters.py` - Advanced filtering and signal validation

## 🔧 Setup

1. **Install Dependencies**:
   ```bash
   pip install pandas numpy scikit-learn requests joblib
   ```

2. **API Configuration**:
   - The Oanda API credentials are already configured in `config.py`
   - Using practice environment for safe testing

## 🚀 Usage

### Quick Start (Recommended)
```bash
python trading_bot_launcher.py
```

### Individual Components

#### Live Trading
```bash
python live_trading_bot.py
```

#### Advanced Signal Generator
```bash
python advanced_signal_generator.py
```

#### Backtesting
```bash
python backtest_system.py
```

#### API Connection Test
```bash
python test_oanda_connection.py
```

## 🎛️ Enhanced Menu System

The main launcher provides an intuitive menu with the following options:

```
🤖 ADVANCED TRADING BOT SYSTEM
════════════════════════════════════════════════════════════════
1. 📈 Rule-Based Trading (Live)
2. 🧠 ML-Integrated Trading (Live)
3. 🔬 Backtesting System
4. 🔗 Test API Connection
5. 📊 View Current Directory
6. ❌ Exit
```

### Backtesting Sub-Menu

Option 3 opens a comprehensive backtesting menu:

```
🔬 BACKTESTING SYSTEM
════════════════════════════════════════════════════════════════
1. 📈 Rule-Based Backtesting
   • Test pure rule-based strategies
   • Fast execution, transparent logic

2. 🧠 ML-Integrated Backtesting
   • Test ML-enhanced strategies
   • AI predictions + rule validation

3. 🔄 Comparative Backtesting
   • Compare rule-based vs ML performance
   • Side-by-side analysis

4. ⬅️ Back to Main Menu
```

## 📊 Live Trading Features

- **Real-time Monitoring**: Fetches live market data every 2 seconds before next candle
- **Signal Detection**: Evaluates all 4 strategies simultaneously
- **Confidence Scoring**: Only shows signals above 60% confidence threshold
- **Detailed Logging**: Shows date, time, direction, price, confidence, and strategy

### Rule-Based Trading Output Format
```
📊 MARKET SCAN - 2025-06-03 00:15:58
Pair        Price       Signal    Confidence  Strategy
EUR_USD     1.14380     BUY       75.0%       S1
GBP_USD     1.35425     NO SIGNAL -           -
```

### ML-Integrated Trading Output Format
```
🧠 ML-ENHANCED MARKET SCAN - 2025-06-03 00:15:58
Pair      Price     ML Signal  Confidence  Rule Signal  Final
EUR_USD   1.14380   BUY        82.3%       BUY          BUY
GBP_USD   1.35425   HOLD       45.2%       SELL         NO SIGNAL
USD_JPY   142.799   SELL       78.9%       HOLD         SELL
```

## 🔬 Backtesting Features

- **Interactive Setup**: Choose candles, pairs, and strategies
- **Detailed Analytics**: Win/loss ratios, accuracy percentages
- **Multiple Strategies**: Test individual or combined strategies
- **Colorful Results**: Easy-to-read tabular output

### Backtesting Output Format
```
📊 S1 (Breakout with Volume) Results:
Metric              Buy            Sell           Overall
Total Signals       15             12             27
Wins               9              8              17
Losses             6              4              10
Accuracy           60.0%          66.7%          63.0%
```

## 🎯 Trading Strategies

### Strategy 1: Breakout with Volume
- Detects price breakouts above resistance or below support
- Confirms with volume analysis and wick patterns
- High confidence signals for strong momentum moves

### Strategy 2: Order Block Strategy  
- Identifies institutional order blocks
- Looks for price rejection at key levels
- Uses trend analysis and volume confirmation

### Strategy 3: Support/Resistance Rejection
- Detects bounces from support levels
- Identifies rejections at resistance levels
- Uses wick analysis for confirmation

### Strategy 4: Trendline Break with Rejection
- Identifies trendline breaks and rejections
- Uses wick patterns for signal confirmation
- Analyzes trend direction for context

## ⚙️ Configuration

### API Settings (config.py)
```python
OANDA_CONFIG = {
    "ACCESS_TOKEN": "your_token_here",
    "ACCOUNT_ID": "your_account_id",
    "BASE_URL": "https://api-fxpractice.oanda.com"
}
```

### Trading Settings
```python
TRADING_CONFIG = {
    "CANDLE_INTERVAL": "M1",     # 1-minute candles
    "FETCH_INTERVAL": 58,        # Fetch 2 seconds before next candle
    "MIN_CONFIDENCE": 0.6        # 60% minimum confidence
}
```

## 🎨 Color Coding

- 🟢 **Green**: BUY signals, success messages, wins
- 🔴 **Red**: SELL signals, error messages, losses  
- 🟡 **Yellow**: HOLD signals, warnings
- 🔵 **Blue**: Information, neutral data
- 🟣 **Purple**: Headers, important sections

## 📈 Performance Monitoring

The system tracks:
- **Signal Accuracy**: Percentage of winning trades
- **Win/Loss Ratios**: Separate tracking for BUY and SELL signals
- **Confidence Levels**: Only high-confidence signals are shown
- **Strategy Performance**: Individual strategy analytics

## 🛡️ Safety Features

- **Practice Environment**: Uses Oanda practice API
- **No Automatic Trading**: Only provides signals, no actual trades
- **Error Handling**: Comprehensive error handling and recovery
- **User Confirmation**: Requires confirmation for major actions

## 🔄 Workflow

1. **Start System**: Use launcher or run individual components
2. **Live Monitoring**: Bot fetches data every minute, 2 seconds before candle close
3. **Signal Generation**: Evaluates all strategies for each currency pair
4. **Display Results**: Shows signals with confidence and strategy information
5. **Backtesting**: Test strategies on historical data for validation

## 📞 Support

For issues or questions:
1. Check the colorful error messages for guidance
2. Verify API connection with test script
3. Ensure all required files are present
4. Check that models are properly trained

## 🎉 Success Metrics

- ✅ API Connection: All 10 currency pairs accessible
- ✅ Real-time Data: Live candle data every minute
- ✅ Strategy Integration: All 4 strategies working
- ✅ Backtesting: Comprehensive analytics available
- ✅ User Interface: Colorful, intuitive CLI
