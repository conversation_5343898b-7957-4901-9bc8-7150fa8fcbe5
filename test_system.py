#!/usr/bin/env python3
"""
System Test Script
Tests all major components to ensure they work correctly
"""

import pandas as pd
import numpy as np
from strategy_engine import StrategyEngine
from backtest_system import BacktestSystem
from utils import print_colored, print_header

def test_strategy_engine():
    """Test the strategy engine with sample data"""
    print_header("🔧 TESTING STRATEGY ENGINE")
    
    # Create sample data
    data = {
        'close': [1.1000, 1.1010, 1.1020, 1.1015, 1.1025, 1.1030, 1.1028, 1.1035, 1.1040, 1.1038, 1.1042, 1.1045, 1.1043, 1.1048, 1.1050, 1.1052],
        'open': [1.0995, 1.1005, 1.1015, 1.1020, 1.1020, 1.1025, 1.1030, 1.1030, 1.1035, 1.1040, 1.1040, 1.1042, 1.1045, 1.1045, 1.1048, 1.1050],
        'high': [1.1005, 1.1015, 1.1025, 1.1025, 1.1030, 1.1035, 1.1035, 1.1040, 1.1045, 1.1045, 1.1045, 1.1048, 1.1048, 1.1050, 1.1055, 1.1055],
        'low': [1.0990, 1.1000, 1.1010, 1.1010, 1.1015, 1.1020, 1.1025, 1.1025, 1.1030, 1.1035, 1.1035, 1.1040, 1.1040, 1.1043, 1.1045, 1.1048],
        'volume': [1000, 1200, 1100, 1300, 1400, 1250, 1350, 1500, 1200, 1400, 1600, 1300, 1450, 1700, 1550, 1800],
        'ema_20': [1.1000, 1.1005, 1.1010, 1.1012, 1.1015, 1.1018, 1.1020, 1.1023, 1.1026, 1.1028, 1.1030, 1.1032, 1.1034, 1.1036, 1.1038, 1.1040],
        'rsi': [45, 48, 52, 50, 55, 58, 56, 60, 62, 60, 63, 65, 63, 67, 68, 66]
    }
    
    df = pd.DataFrame(data)
    se = StrategyEngine()
    
    # Test all strategies
    strategies = ['S1', 'S2', 'S3', 'S4']
    for strategy in strategies:
        try:
            if strategy == 'S1':
                signal, confidence = se.evaluate_strategy_1(df)
            elif strategy == 'S2':
                signal, confidence = se.evaluate_strategy_2(df)
            elif strategy == 'S3':
                signal, confidence = se.evaluate_strategy_3(df)
            elif strategy == 'S4':
                signal, confidence = se.evaluate_strategy_4(df)
            
            print_colored(f"✅ Strategy {strategy}: Signal={signal}, Confidence={confidence:.2f}", "SUCCESS")
        except Exception as e:
            print_colored(f"❌ Strategy {strategy} failed: {str(e)}", "ERROR")
    
    # Test combined evaluation
    try:
        result = se.evaluate_all_strategies(df)
        print_colored(f"✅ Combined evaluation: {result['signal']} (Confidence: {result['confidence']:.2f})", "SUCCESS")
    except Exception as e:
        print_colored(f"❌ Combined evaluation failed: {str(e)}", "ERROR")

def test_imports():
    """Test that all major components can be imported"""
    print_header("📦 TESTING IMPORTS")
    
    components = [
        ('strategy_engine', 'StrategyEngine'),
        ('live_trading_bot', 'LiveTradingBot'),
        ('backtest_system', 'BacktestSystem'),
        ('advanced_signal_generator', 'AdvancedSignalGenerator'),
        ('trading_bot_launcher', None),
        ('utils', None),
        ('config', None)
    ]
    
    for module_name, class_name in components:
        try:
            module = __import__(module_name)
            if class_name:
                getattr(module, class_name)
            print_colored(f"✅ {module_name} imported successfully", "SUCCESS")
        except Exception as e:
            print_colored(f"❌ {module_name} import failed: {str(e)}", "ERROR")

def main():
    """Run all tests"""
    print_header("🧪 SYSTEM TESTING")
    print_colored("Testing all major components...", "INFO")
    print()
    
    test_imports()
    print()
    test_strategy_engine()
    
    print()
    print_header("🎉 TESTING COMPLETED")
    print_colored("All tests completed. Check results above.", "SUCCESS")

if __name__ == "__main__":
    main()
